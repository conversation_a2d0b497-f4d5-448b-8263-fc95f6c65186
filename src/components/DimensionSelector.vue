<template>
  <div class="dimension-selector">
    <div class="selector-body">
      <!-- Step 1: Show available categories if no category is selected -->
      <div v-if="!selectedCategory" class="category-selection">
        <div class="level-label">选择分类</div>
        <div class="level level-1">
          <div
            v-for="category in categories"
            :key="category.id"
            class="cat-item"
            @click="selectCategory(category.id)"
          >
            {{ category.name }}
          </div>
        </div>
        <div v-if="categories.length === 0" class="empty-tip">
          <div>暂无可用分类</div>
        </div>
      </div>

      <!-- Step 2: Show hierarchical categories and dimensions after category selection -->
      <div v-else class="category-levels">
        <div class="selected-category-header">
          <button class="back-btn" @click="selectedCategory = null">← 返回分类选择</button>
          <span class="selected-category-name">{{ selectedCategory }}</span>
        </div>

        <div v-if="isLoadingDimensions" class="loading-tip">
          <div>正在加载维度...</div>
        </div>

        <div v-else-if="organizedCategories.length > 0">
          <div class="level-label">一级分类</div>
          <div class="level level-1">
            <div
              v-for="cat1 in organizedCategories"
              :key="cat1.id"
              :class="['cat-item', { active: cat1.id === selectedCat1 }]"
              @click="selectCat1(cat1.id)"
            >
              {{ cat1.name }}
            </div>
          </div>
          <div v-if="cat2List.length">
            <div class="level-label">二级分类</div>
            <div class="level level-2">
              <div
                v-for="cat2 in cat2List"
                :key="cat2.id"
                :class="['cat-item', { active: cat2.id === selectedCat2 }]"
                @click="selectCat2(cat2.id)"
              >
                {{ cat2.name }}
              </div>
            </div>
          </div>
          <div v-if="cat3List.length">
            <div class="level-label">三级分类</div>
            <div class="level level-3">
              <div
                v-for="cat3 in cat3List"
                :key="cat3.id"
                :class="['cat-item', { active: cat3.id === selectedCat3 }]"
                @click="selectCat3(cat3.id)"
              >
                {{ cat3.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Dimension list (shown when dimensions are available) -->
      <div class="dimension-list" v-if="dimensionList.length">
        <div
          v-for="dim in dimensionList"
          :key="dim.id"
          class="dimension-item"
          @click="$emit('select', dim)"
        >
          <span>{{ dim.name }}</span>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else-if="selectedCategory && !isLoadingDimensions" class="empty-tip">
        <div>请选择分类以查看维度</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import axios from 'axios'
import { handleError } from '@/utils/errorHandler'

const statesStore = useStates()
const { setAvailableDimensions } = statesStore
const { availableDimensions, availableDimensionCategories, states } = storeToRefs(statesStore)

// Use categories from store (fetched by parent component)
const categories = computed(() => {
  return availableDimensionCategories.value || []
})

// Loading state for fetching dimensions
const isLoadingDimensions = ref(false)

// Fetch dimensions for selected category
const fetchDimensionsForCategory = async (categoryId) => {
  if (isLoadingDimensions.value) {
    return
  }

  // Check if we have granularity selected and indicators
  const granularityComponent = states.value.find((state) => state.componentName === '粒度')
  const indicatorComponent = states.value.find((state) => state.componentName === '目标指标')

  if (!granularityComponent?.granularityId) {
    console.log('No granularity selected, skipping dimensions fetch')
    return
  }

  try {
    isLoadingDimensions.value = true
    const params = {
      key: granularityComponent.granularityId,
      category_lv_1: categoryId, // Use selected category
      indices: indicatorComponent?.indices?.map((idx) => {
        // Use indexId for API calls (key_id parameter)
        if (typeof idx === 'object' && idx.indexId) {
          return idx.indexId
        }
        // Fallback for legacy data format
        return typeof idx === 'string' ? idx : idx.indexName
      }) || [],
    }

    console.log('Fetching dimensions for category with params:', params)
    const resp = await axios.get('/api/v1/config/data_prep/dimensions', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    console.log('Dimensions API response:', resp.data)

    if (resp.data.error_code === 0) {
      // Store complete response data including all dimension metadata
      setAvailableDimensions(resp.data.data || [])
      console.log('Stored complete dimension data for category:', resp.data.data)
    } else {
      console.error('API error:', resp.data.message)
      // Use mock data for development
      setAvailableDimensions([
        {
          id: '101',
          name: 'region',
          display_name: '地区',
          category_lv_1: categoryId,
          category_lv_2: '信用贷款处',
          category_lv_3: '地区类',
          is_permission: 1,
        },
        {
          id: '102',
          name: 'branch',
          display_name: '所属分行',
          category_lv_1: categoryId,
          category_lv_2: '信用贷款处',
          category_lv_3: '分行类',
          is_permission: 1,
        },
      ])
    }
  } catch (error) {
    console.error('Failed to fetch dimensions for category:', error)

    // Use the new error handler to classify and display errors appropriately
    const retryAction = () => fetchDimensionsForCategory(categoryId)
    handleError(error, '获取维度数据失败', retryAction)

    // Use mock data for development/network errors as fallback
    setAvailableDimensions([
      {
        id: '101',
        name: 'region',
        display_name: '地区',
        category_lv_1: categoryId,
        category_lv_2: '信用贷款处',
        category_lv_3: '地区类',
        is_permission: 1,
      },
      {
        id: '102',
        name: 'branch',
        display_name: '所属分行',
        category_lv_1: categoryId,
        category_lv_2: '信用贷款处',
        category_lv_3: '分行类',
        is_permission: 1,
      },
    ])
  } finally {
    isLoadingDimensions.value = false
  }
}

const selectedCategory = ref(null)

// Organize dimensions by categories (for display after fetching)
const organizedCategories = computed(() => {
  if (!availableDimensions.value.length) return []

  const categoryMap = new Map()

  availableDimensions.value.forEach((dimension) => {
    if (!dimension.is_permission) return // Skip non-permitted dimensions

    const cat1Key = dimension.category_lv_1
    const cat2Key = dimension.category_lv_2 || 'default'
    const cat3Key = dimension.category_lv_3 || 'default'

    if (!categoryMap.has(cat1Key)) {
      categoryMap.set(cat1Key, {
        id: cat1Key,
        name: cat1Key,
        children: new Map(),
      })
    }

    const cat1 = categoryMap.get(cat1Key)
    if (!cat1.children.has(cat2Key)) {
      cat1.children.set(cat2Key, {
        id: `${cat1Key}-${cat2Key}`,
        name: cat2Key,
        children: new Map(),
      })
    }

    const cat2 = cat1.children.get(cat2Key)
    if (!cat2.children.has(cat3Key)) {
      cat2.children.set(cat3Key, {
        id: `${cat1Key}-${cat2Key}-${cat3Key}`,
        name: cat3Key,
        dimensions: [],
      })
    }

    const cat3 = cat2.children.get(cat3Key)
    cat3.dimensions.push({
      id: dimension.id,
      name: dimension.display_name,
      originalData: dimension,
    })
  })

  // Convert Maps to Arrays
  const result = Array.from(categoryMap.values()).map((cat1) => ({
    ...cat1,
    children: Array.from(cat1.children.values()).map((cat2) => ({
      ...cat2,
      children: Array.from(cat2.children.values()),
    })),
  }))

  return result
})

const selectedCat1 = ref(null)
const selectedCat2 = ref(null)
const selectedCat3 = ref(null)

const cat2List = computed(() => {
  const cat1 = categories.value.find((c) => c.id === selectedCat1.value)
  return cat1 ? cat1.children : []
})
const cat3List = computed(() => {
  const cat2 = cat2List.value.find((c) => c.id === selectedCat2.value)
  return cat2 ? cat2.children : []
})
const dimensionList = computed(() => {
  const cat3 = cat3List.value.find((c) => c.id === selectedCat3.value)
  return cat3 ? cat3.dimensions : []
})

function selectCat1(id) {
  selectedCat1.value = id
  selectedCat2.value = null
  selectedCat3.value = null
}
function selectCat2(id) {
  selectedCat2.value = id
  selectedCat3.value = null
}
function selectCat3(id) {
  selectedCat3.value = id
}
</script>

<style scoped>
.dimension-selector {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}
.selector-body {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  gap: 24px;
  overflow: hidden;
}
.category-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}
.level-label {
  font-size: 0.98rem;
  font-weight: 500;
  color: #1976d2;
  margin: 8px 0 4px 0;
  padding-left: 2px;
}
.level {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 4px;
}
.cat-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
}
.cat-item.active {
  background: #1976d2;
  color: #fff;
}
.dimension-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}
.dimension-item {
  padding: 10px 16px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  transition:
    background 0.2s,
    color 0.2s;
}
.dimension-item:hover {
  background: #1976d2;
  color: #fff;
}
.empty-tip {
  color: #aaa;
  font-size: 1rem;
  margin-top: 40px;
}
</style>
