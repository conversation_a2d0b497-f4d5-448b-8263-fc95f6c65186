<template>
  <div class="index-selector">
    <div class="selector-body">
      <!-- Step 1: Show available categories if no category is selected -->
      <div v-if="!selectedCategory" class="category-selection">
        <div class="level-label">选择分类</div>
        <div class="level level-1">
          <div
            v-for="category in categories"
            :key="category.id"
            class="cat-item"
            @click="selectCategory(category.id)"
          >
            {{ category.name }}
          </div>
        </div>
        <div v-if="categories.length === 0" class="empty-tip">
          <div>暂无可用分类</div>
        </div>
      </div>

      <!-- Step 2: Show hierarchical categories and indices after category selection -->
      <div v-else class="category-levels">
        <div class="selected-category-header">
          <button class="back-btn" @click="selectedCategory = null">← 返回分类选择</button>
          <span class="selected-category-name">{{ selectedCategory }}</span>
        </div>

        <div v-if="isLoadingIndices" class="loading-tip">
          <div>正在加载指标...</div>
        </div>

        <div v-else-if="organizedCategories.length > 0">
          <div class="level-label">一级分类</div>
          <div class="level level-1">
            <div
              v-for="cat1 in organizedCategories"
              :key="cat1.id"
              :class="['cat-item', { active: cat1.id === selectedCat1 }]"
              @click="selectCat1(cat1.id)"
            >
              {{ cat1.name }}
            </div>
          </div>
          <div v-if="cat2List.length">
            <div class="level-label">二级分类</div>
            <div class="level level-2">
              <div
                v-for="cat2 in cat2List"
                :key="cat2.id"
                :class="['cat-item', { active: cat2.id === selectedCat2 }]"
                @click="selectCat2(cat2.id)"
              >
                {{ cat2.name }}
              </div>
            </div>
          </div>
          <div v-if="cat3List.length">
            <div class="level-label">三级分类</div>
            <div class="level level-3">
              <div
                v-for="cat3 in cat3List"
                :key="cat3.id"
                :class="['cat-item', { active: cat3.id === selectedCat3 }]"
                @click="selectCat3(cat3.id)"
              >
                {{ cat3.name }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Index list (shown when indices are available) -->
      <div class="index-list" v-if="indexList.length">
        <div
          v-for="idx in indexList"
          :key="idx.id"
          class="index-item"
          @click="$emit('select', idx)"
        >
          <span>{{ idx.name }}</span>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else-if="selectedCategory && !isLoadingIndices" class="empty-tip">
        <div>请选择分类以查看指标</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useStates } from '../store/states'
import axios from 'axios'
import { handleError } from '@/utils/errorHandler'

const statesStore = useStates()
const { setAvailableIndicators } = statesStore
const { availableIndicators, availableIndicatorCategories, states } = storeToRefs(statesStore)

// Use categories from store (fetched by parent component)
const categories = computed(() => {
  return availableIndicatorCategories.value || []
})

// Loading state for fetching indices
const isLoadingIndices = ref(false)

// Fetch indices for selected category
const fetchIndicesForCategory = async (categoryId) => {
  if (isLoadingIndices.value) {
    return
  }

  // Check if we have granularity selected
  const granularityComponent = states.value.find((state) => state.componentName === '粒度')
  if (!granularityComponent?.granularity) {
    console.log('No granularity selected, skipping indices fetch')
    return
  }

  try {
    isLoadingIndices.value = true
    const params = {
      key: granularityComponent.granularityId,
      category_lv_1: categoryId, // Use selected category
    }

    console.log('Fetching indices for category with params:', params)
    const resp = await axios.get('/api/v1/config/data_prep/indices', {
      params,
      headers: {
        token: localStorage.getItem('token'),
      },
    })
    console.log('Indices API response:', resp.data)

    if (resp.data.error_code === 0) {
      // Store complete response data including all indicator metadata
      setAvailableIndicators(resp.data.data || [])
      console.log('Stored complete indicator data for category:', resp.data.data)
    } else {
      console.error('API error:', resp.data.message)
      // Use mock data for development
      setAvailableIndicators([
        {
          id: '101',
          name: 'overdue_amount',
          display_name: '逾期金额',
          category_lv_1: categoryId,
          category_lv_2: '信用贷款处',
          category_lv_3: '逾期类',
          is_permission: 1,
        },
        {
          id: '102',
          name: 'bill_amount',
          display_name: '账单金额',
          category_lv_1: categoryId,
          category_lv_2: '信用贷款处',
          category_lv_3: '账单类',
          is_permission: 1,
        },
      ])
    }
  } catch (error) {
    console.error('Failed to fetch indices for category:', error)

    // Use the new error handler to classify and display errors appropriately
    const retryAction = () => fetchIndicesForCategory(categoryId)
    handleError(error, '获取指标数据失败', retryAction)

    // Use mock data for development/network errors as fallback
    setAvailableIndicators([
      {
        id: '101',
        name: 'overdue_amount',
        display_name: '逾期金额',
        category_lv_1: categoryId,
        category_lv_2: '信用贷款处',
        category_lv_3: '逾期类',
        is_permission: 1,
      },
      {
        id: '102',
        name: 'bill_amount',
        display_name: '账单金额',
        category_lv_1: categoryId,
        category_lv_2: '信用贷款处',
        category_lv_3: '账单类',
        is_permission: 1,
      },
    ])
  } finally {
    isLoadingIndices.value = false
  }
}

const selectedCategory = ref(null)

// Organize indicators by categories (for display after fetching)
const organizedCategories = computed(() => {
  if (!availableIndicators.value.length) return []

  const categoryMap = new Map()

  availableIndicators.value.forEach((indicator) => {
    if (!indicator.is_permission) return // Skip non-permitted indicators

    const cat1Key = indicator.category_lv_1
    const cat2Key = indicator.category_lv_2 || 'default'
    const cat3Key = indicator.category_lv_3 || 'default'

    if (!categoryMap.has(cat1Key)) {
      categoryMap.set(cat1Key, {
        id: cat1Key,
        name: cat1Key,
        children: new Map(),
      })
    }

    const cat1 = categoryMap.get(cat1Key)
    if (!cat1.children.has(cat2Key)) {
      cat1.children.set(cat2Key, {
        id: `${cat1Key}-${cat2Key}`,
        name: cat2Key,
        children: new Map(),
      })
    }

    const cat2 = cat1.children.get(cat2Key)
    if (!cat2.children.has(cat3Key)) {
      cat2.children.set(cat3Key, {
        id: `${cat1Key}-${cat2Key}-${cat3Key}`,
        name: cat3Key,
        indices: [],
      })
    }

    const cat3 = cat2.children.get(cat3Key)
    cat3.indices.push({
      id: indicator.id,
      name: indicator.display_name,
      originalData: indicator,
    })
  })

  // Convert Maps to Arrays
  const result = Array.from(categoryMap.values()).map((cat1) => ({
    ...cat1,
    children: Array.from(cat1.children.values()).map((cat2) => ({
      ...cat2,
      children: Array.from(cat2.children.values()),
    })),
  }))

  return result
})

const selectedCat1 = ref(null)
const selectedCat2 = ref(null)
const selectedCat3 = ref(null)

const cat2List = computed(() => {
  const cat1 = organizedCategories.value.find((c) => c.id === selectedCat1.value)
  return cat1 ? cat1.children : []
})
const cat3List = computed(() => {
  const cat2 = cat2List.value.find((c) => c.id === selectedCat2.value)
  return cat2 ? cat2.children : []
})
const indexList = computed(() => {
  const cat3 = cat3List.value.find((c) => c.id === selectedCat3.value)
  return cat3 ? cat3.indices : []
})

function selectCat1(id) {
  selectedCat1.value = id
  selectedCat2.value = null
  selectedCat3.value = null
}
function selectCat2(id) {
  selectedCat2.value = id
  selectedCat3.value = null
}
function selectCat3(id) {
  selectedCat3.value = id
}

// Handle category selection (first step)
function selectCategory(categoryId) {
  selectedCategory.value = categoryId
  // Clear previous selections
  selectedCat1.value = null
  selectedCat2.value = null
  selectedCat3.value = null
  // Fetch indices for this category
  fetchIndicesForCategory(categoryId)
}
</script>

<style scoped>
.index-selector {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}
.selector-body {
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 20px;
  gap: 24px;
  overflow: hidden;
}
.category-selection {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}

.category-levels {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  max-width: 220px;
}

.selected-category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.back-btn {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #666;
  transition: all 0.2s;
}

.back-btn:hover {
  background: #e8e8e8;
  color: #333;
}

.selected-category-name {
  font-weight: 500;
  color: #1976d2;
  font-size: 1rem;
}

.loading-tip {
  padding: 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}
.level-label {
  font-size: 0.98rem;
  font-weight: 500;
  color: #1976d2;
  margin: 8px 0 4px 0;
  padding-left: 2px;
}
.level {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 4px;
}
.cat-item {
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  background: #f7faff;
  color: #333;
  transition:
    background 0.2s,
    color 0.2s;
}
.cat-item.active {
  background: #1976d2;
  color: #fff;
}
.index-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
}
.index-item {
  padding: 10px 16px;
  border-radius: 6px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 1rem;
  color: #1976d2;
  transition:
    background 0.2s,
    color 0.2s;
}
.index-item:hover {
  background: #1976d2;
  color: #fff;
}
.empty-tip {
  color: #aaa;
  font-size: 1rem;
  margin-top: 40px;
}
</style>
