import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { v4 as uuidv4 } from 'uuid'

const getDefaultStates = () => {
  return [
    {
      componentId: uuidv4(),
      componentName: '目标指标',
      indices: [],
    },
    {
      componentId: uuidv4(),
      componentName: '目标维度',
      dimensions: [],
    },
    {
      componentId: uuidv4(),
      componentName: '粒度',
      granularity: '',
      granularityId: '',
      granularityName: '',
    },
    {
      componentId: uuidv4(),
      componentName: '日期',
      dates: [],
    },
    {
      componentId: uuidv4(),
      componentName: 'trendAnalysis',
      selectedTrends: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'corrAnalysis',
      selectedObjects: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'table',
      activeSheet: '',
      sheetData: [],
      semanticsConfig: {},
    },
    {
      componentId: uuidv4(),
      componentName: 'dashboardCharts',
      charts: [],
    },
  ]
}

export const useStates = defineStore('states', () => {
  const pageName = ref('')
  const title = ref('')
  const taskName = ref('未命名') // Default task name for guided analysis
  const defaultStates = getDefaultStates()
  const stateIds = ref(new Set(defaultStates.map((state) => state.componentId)))
  const states = ref(defaultStates)
  const chatMessages = ref([])
  const username = ref('')
  const userRole = ref('') // 'admin' or ''
  // const authToken = ref('a1234567890123456789012345678901234567890')
  const tokenExpireTime = ref('')
  const userInfo = ref(null)
  const availableIndicators = ref([])
  const availableDimensions = ref([])
  const availableIndicatorCategories = ref([])
  const availableDimensionCategories = ref([])

  // System error state for global error mask
  const systemError = ref({
    show: false,
    title: '',
    message: '',
    errorCode: null,
    retryAction: null,
    timestamp: null,
  })

  const getStates = computed(() => {
    return {
      pageName: pageName.value,
      title: title.value,
      timestamp: new Date().getTime(),
      states: states.value,
    }
  })

  function updateComponent(componentId, newStates) {
    this.$patch((s) => {
      if (!s.stateIds.has(componentId)) {
        s.stateIds.add(componentId)
        s.states.push({ componentId, ...newStates })
      } else {
        s.states.forEach((state) => {
          if (state.componentId === componentId) {
            Object.assign(state, newStates)
          }
        })
      }
    })
  }

  function deleteComponent(componentId) {
    this.$patch((s) => {
      s.stateIds.delete(componentId)
      s.states = s.states.filter((state) => state.componentId !== componentId)
    })
  }

  function setChatMessages(msgs) {
    chatMessages.value = msgs
  }

  function appendChatMessage(msg) {
    chatMessages.value.push(msg)
  }

  function clearChatMessages() {
    chatMessages.value = []
  }

  function setUsername(name) {
    username.value = name
  }

  function clearUsername() {
    username.value = ''
  }

  function setUserRole(role) {
    userRole.value = role
  }

  function clearUserRole() {
    userRole.value = ''
  }

  function setAuthToken(token) {
    // authToken.value = token
    localStorage.setItem('token', token)
  }

  function clearAuthToken() {
    // authToken.value = ''
    localStorage.removeItem('token')
  }

  function setTokenExpireTime(expireTime) {
    tokenExpireTime.value = expireTime
  }

  function clearTokenExpireTime() {
    tokenExpireTime.value = ''
  }

  function setUserInfo(info) {
    userInfo.value = info
  }

  function clearUserInfo() {
    userInfo.value = null
  }

  function clearAllAuthData() {
    clearUsername()
    clearUserRole()
    clearAuthToken()
    clearTokenExpireTime()
    clearUserInfo()
    // Clear localStorage as well
    localStorage.removeItem('auth_token')
    localStorage.removeItem('token_expire_time')
    localStorage.removeItem('user_info')
  }

  function setAvailableIndicators(indicators) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableIndicators = indicators
    })
  }

  function clearAvailableIndicators() {
    availableIndicators.value = []
  }

  function setAvailableDimensions(dimensions) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableDimensions = dimensions
    })
  }

  function clearAvailableDimensions() {
    availableDimensions.value = []
  }

  function setAvailableIndicatorCategories(categories) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableIndicatorCategories = categories
    })
  }

  function clearAvailableIndicatorCategories() {
    availableIndicatorCategories.value = []
  }

  function setAvailableDimensionCategories(categories) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableDimensionCategories = categories
    })
  }

  function clearAvailableDimensionCategories() {
    availableDimensionCategories.value = []
  }

  // Task name management functions
  function setTaskName(name) {
    taskName.value = name || '未命名'
  }

  function clearTaskName() {
    taskName.value = '未命名'
  }

  // System error management functions
  function showSystemError(title, message, errorCode = null, retryAction = null) {
    systemError.value = {
      show: true,
      title,
      message,
      errorCode,
      retryAction,
      timestamp: new Date().getTime(),
    }
  }

  function hideSystemError() {
    systemError.value = {
      show: false,
      title: '',
      message: '',
      errorCode: null,
      retryAction: null,
      timestamp: null,
    }
  }

  function clearSystemError() {
    hideSystemError()
  }

  return {
    pageName,
    title,
    taskName,
    setTaskName,
    clearTaskName,
    states,
    stateIds,
    getStates,
    updateComponent,
    deleteComponent,
    chatMessages,
    setChatMessages,
    appendChatMessage,
    clearChatMessages,
    username,
    setUsername,
    clearUsername,
    userRole,
    setUserRole,
    clearUserRole,
    setAuthToken,
    clearAuthToken,
    tokenExpireTime,
    setTokenExpireTime,
    clearTokenExpireTime,
    userInfo,
    setUserInfo,
    clearUserInfo,
    clearAllAuthData,
    availableIndicators,
    setAvailableIndicators,
    clearAvailableIndicators,
    availableDimensions,
    setAvailableDimensions,
    clearAvailableDimensions,
    availableIndicatorCategories,
    setAvailableIndicatorCategories,
    clearAvailableIndicatorCategories,
    availableDimensionCategories,
    setAvailableDimensionCategories,
    clearAvailableDimensionCategories,
    systemError,
    showSystemError,
    hideSystemError,
    clearSystemError,
  }
})
